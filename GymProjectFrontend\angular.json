{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"gymproject": {"projectType": "application", "schematics": {"@schematics/angular:component": {"standalone": false}, "@schematics/angular:directive": {"standalone": false}, "@schematics/angular:pipe": {"standalone": false}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/gymproject", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["@angular/material/prebuilt-themes/azure-blue.css", "./node_modules/bootstrap/dist/css/bootstrap.min.css", "src/styles.css", "./node_modules/ngx-toastr/toastr.css", "node_modules/@fortawesome/fontawesome-svg-core/styles.css", "src/assets/css/modern-components.css"], "scripts": ["./node_modules/jquery/dist/jquery.min.js", "./node_modules/bootstrap/dist/js/bootstrap.bundle.min.js"], "server": "src/main.server.ts", "prerender": true, "ssr": {"entry": "server.ts"}}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "15mb", "maximumError": "15mb"}, {"type": "anyComponentStyle", "maximumWarning": "15kb", "maximumError": "15kb"}], "outputHashing": "all"}, "staging": {"budgets": [{"type": "initial", "maximumWarning": "15mb", "maximumError": "15mb"}, {"type": "anyComponentStyle", "maximumWarning": "15kb", "maximumError": "15kb"}], "outputHashing": "all", "optimization": true, "sourceMap": false, "extractLicenses": true}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "gymproject:build:production"}, "staging": {"buildTarget": "gymproject:build:staging"}, "development": {"buildTarget": "gymproject:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "gymproject:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["@angular/material/prebuilt-themes/azure-blue.css", "src/styles.css"], "scripts": []}}}}}, "cli": {"analytics": "e3cd9d18-e6f3-46f3-8aee-684fe0e24487"}}