Stack trace:
Frame         Function      Args
0007FFFFBBB0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFAAB0) msys-2.0.dll+0x1FE8E
0007FFFFBBB0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBE88) msys-2.0.dll+0x67F9
0007FFFFBBB0  000210046832 (000210286019, 0007FFFFBA68, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFBBB0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBBB0  000210068E24 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBE90  00021006A225 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFBCD320000 ntdll.dll
7FFBCBC80000 KERNEL32.DLL
7FFBCA8C0000 KERNELBASE.dll
7FFBCC370000 USER32.dll
7FFBCA520000 win32u.dll
7FFBCD130000 GDI32.dll
7FFBCACB0000 gdi32full.dll
7FFBCA470000 msvcp_win.dll
7FFBCAF70000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFBCB200000 advapi32.dll
7FFBCC5D0000 msvcrt.dll
7FFBCB150000 sechost.dll
7FFBCBB60000 RPCRT4.dll
7FFBC9B90000 CRYPTBASE.DLL
7FFBCA820000 bcryptPrimitives.dll
7FFBCD0F0000 IMM32.DLL
