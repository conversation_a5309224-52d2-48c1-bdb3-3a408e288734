export const environment = {
  mode: 'dev', // 'dev' | 'staging' | 'canlı' - <PERSON><PERSON>e bu satırı değiştirin!
  
  // Otomatik konfigürasyon - Manuel değiştirmeyin!
  get production() {
    return this.mode === 'canlı';
  },
  
  get apiUrl() {
    switch (this.mode) {
      case 'dev':
        return 'http://localhost:5165/api/';
      case 'staging':
        return 'https://staging.gymkod.com/api/';
      case 'canlı':
        return 'https://admin.gymkod.com/api/';
      default:
        return 'http://localhost:5165/api/';
    }
  },
  
  get baseUrl() {
    switch (this.mode) {
      case 'dev':
        return 'http://localhost:4200';
      case 'staging':
        return 'https://staging.gymkod.com';
      case 'canlı':
        return 'https://admin.gymkod.com';
      default:
        return 'http://localhost:4200';
    }
  },
  
  get environmentName() {
    switch (this.mode) {
      case 'dev':
        return 'Development';
      case 'staging':
        return 'Staging';
      case 'canlı':
        return 'Production';
      default:
        return 'Development';
    }
  }
};
