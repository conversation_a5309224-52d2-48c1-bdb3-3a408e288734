using System;
using Autofac;
using Autofac.Extensions.DependencyInjection;
using Business.Abstract;
using Business.Concrete;
using Business.DependencyResolvers.Autofac;
using Core.Utilities.IoC;
using Core.Utilities.Security.Encryption;
using Core.Utilities.Security.JWT;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using Core.Extensions;
using Core.DependencyResolvers;
using Core.Extentions;
using Core.CrossCuttingConcerns.Logging.FileLogger;
using Core.CrossCuttingConcerns.Logging;
using System.Diagnostics;
using AspNetCoreRateLimit;

var builder = WebApplication.CreateBuilder(args);

builder.Host.UseServiceProviderFactory(new AutofacServiceProviderFactory())
    .ConfigureContainer<ContainerBuilder>(builder =>
    {
        builder.RegisterModule(new AutofacBusinessModule());
    });

// Add services to the container.
builder.Services.AddControllers();
builder.Services.AddCors();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();
builder.Services.AddMemoryCache();
builder.Services.AddHttpContextAccessor();
builder.Services.AddSingleton<FileLoggerService>();
builder.Services.AddSingleton<PerformanceLoggerService>();
builder.Services.AddSingleton<ILogService, FileLoggerService>();
builder.Services.AddSingleton<Stopwatch>();

// Environment-based configuration
var environment = builder.Configuration["Environment"];
var environmentKey = environment switch
{
    "dev" => "Development",
    "staging" => "Staging",
    "canlı" => "Production",
    _ => "Development"
};

var tokenOptions = builder.Configuration.GetSection($"TokenOptions:{environmentKey}").Get<TokenOptions>();

builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidIssuer = tokenOptions.Issuer,
            ValidAudience = tokenOptions.Audience,
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = SecurityKeyHelper.CreateSecurityKey(tokenOptions.SecurityKey)
        };

        // Token doğrulama hatalarını yakalayıp uygun HTTP yanıtları döndürme
        options.Events = new JwtBearerEvents
        {
            OnChallenge = context =>
            {
                // 401 Unauthorized yanıtı özelleştirme
                context.HandleResponse();
                context.Response.StatusCode = 401;
                context.Response.ContentType = "application/json";
                var result = System.Text.Json.JsonSerializer.Serialize(new { success = false, message = "Oturum süresi dolmuş veya geçersiz. Lütfen tekrar giriş yapın." });
                return context.Response.WriteAsync(result);
            },
            OnForbidden = context =>
            {
                // 403 Forbidden yanıtı özelleştirme
                context.Response.StatusCode = 403;
                context.Response.ContentType = "application/json";
                var result = System.Text.Json.JsonSerializer.Serialize(new { success = false, message = "Bu işlem için yetkiniz bulunmamaktadır." });
                return context.Response.WriteAsync(result);
            }
        };
    });

builder.Services.AddDependencyResolvers(new ICoreModule[] { new CoreModule() });

// AspNetCoreRateLimit konfigürasyonu
builder.Services.AddOptions();
builder.Services.AddMemoryCache();
builder.Services.Configure<IpRateLimitOptions>(builder.Configuration.GetSection("IpRateLimiting"));
builder.Services.Configure<IpRateLimitPolicies>(builder.Configuration.GetSection("IpRateLimitPolicies"));
builder.Services.AddInMemoryRateLimiting();
builder.Services.AddSingleton<IRateLimitConfiguration, RateLimitConfiguration>();
var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

// Swagger her zaman kullanılabilir
app.UseSwagger();
app.UseSwaggerUI();

app.ConfigureCustomExceptionMiddleware();

// Environment-based CORS configuration
var allowedOrigins = builder.Configuration.GetSection($"AllowedOrigins:{environmentKey}").Get<string[]>();
app.UseCors(corsBuilder => corsBuilder
    .WithOrigins(allowedOrigins ?? new[] { "*" })
    .AllowAnyHeader()
    .AllowAnyMethod()
    .AllowCredentials());

// Development ortamında HTTPS redirect'i devre dışı bırak (mobil erişim için)
if (!app.Environment.IsDevelopment())
{
    app.UseHttpsRedirection();
}

// AspNetCoreRateLimit middleware'i ekleniyor
app.UseIpRateLimiting();

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

app.Run();